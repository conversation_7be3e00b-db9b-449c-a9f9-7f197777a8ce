import React, { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import logo from '/logo.svg'; // Adjust the path/extension as needed

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState(null);
  const dropdownRefs = useRef({});

  // Tool categories
  const categories = [
    {
      name: 'Calculators',
      tools: [
        { name: 'Simple Calculator', path: '/simple-calculator' },
        { name: 'Scientific Calculator', path: '/scientific-calculator' },
        { name: 'Age Calculator', path: '/age-calculator' },
        { name: 'BMI Calculator', path: '/bmi-calculator' },
        { name: 'Percentage Calculator', path: '/percentage-calculator' },
        { name: 'Date Difference Calculator', path: '/date-difference-calculator' },
      ]
    },
    {
      name: 'Converters',
      tools: [
        { name: 'Unit Converter', path: '/unit-converter' },
        { name: 'Number to Words Converter', path: '/number-to-words-converter' },
        { name: 'Text Case Converter', path: '/text-case-converter' },
      ]
    },
    {
      name: 'Time Tools',
      tools: [
        { name: 'Countdown Timer', path: '/countdown-timer' },
        { name: 'Days Until Calculator', path: '/days-until-calculator' },
        { name: 'World Clock', path: '/world-clock' },
      ]
    },
    {
      name: 'Design Tools',
      tools: [
        { name: 'Color Palette Generator', path: '/color-palette-generator' },
        { name: 'Gradient Generator', path: '/gradient-generator' },
      ]
    },
    {
      name: 'Text Tools',
      tools: [
        { name: 'Lorem Ipsum Generator', path: '/lorem-ipsum-generator' },
        { name: 'Text Counter', path: '/text-counter' },
      ]
    },
    {
      name: 'Web Dev Tools',
      tools: [
        { name: 'Responsive Screen Tester', path: '/responsive-screen-tester' },
      ]
    },
    {
      name: 'Security',
      tools: [
        { name: 'Password Generator', path: '/password-generator' },
      ]
    },
  ];

  // Toggle mobile menu
  const toggleMenu = () => {
    setIsOpen(!isOpen);
    setActiveDropdown(null);
  };

  // Toggle dropdown
  const toggleDropdown = (category) => {
    setActiveDropdown(activeDropdown === category ? null : category);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (activeDropdown) {
        const dropdownRef = dropdownRefs.current[activeDropdown];
        if (dropdownRef && !dropdownRef.contains(event.target)) {
          setActiveDropdown(null);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [activeDropdown]);

  // Handle link click in mobile view
  const handleLinkClick = () => {
    if (isOpen) {
      setIsOpen(false);
      setActiveDropdown(null);
    }
  };

  return (
    <nav className="bg-gray-800 text-white p-4 shadow-md sticky top-0 z-50">
      <div className="container mx-auto flex flex-wrap justify-between items-center">
        <Link to="/" className="flex items-center text-2xl font-bold hover:text-gray-300">
          <img src={logo} alt="ToollyHub Logo" className="h-25 w-25 mr-2" />
          {/* <span className="hidden sm:block">ToollyHub</span> */}
        </Link>

        {/* Hamburger Menu Button for Mobile */}
        <div className="lg:hidden">
          <button
            onClick={toggleMenu}
            className="text-white focus:outline-none focus:text-gray-300"
            aria-label="Toggle menu"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              {isOpen ? (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              ) : (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M4 6h16M4 12h16m-7 6h7"
                />
              )}
            </svg>
          </button>
        </div>

        {/* Desktop Navigation */}
        <div className="hidden lg:flex lg:items-center lg:w-auto">
          <ul className="flex space-x-1 xl:space-x-4 items-center text-base">
            <li>
              <Link to="/" className="block px-3 py-2 hover:text-gray-300 rounded-md hover:bg-gray-700 transition-colors">
                Home
              </Link>
            </li>
            
            {categories.map((category) => (
              <li key={category.name} className="relative group" 
                  ref={(el) => dropdownRefs.current[category.name] = el}>
                <button 
                  className="flex items-center px-3 py-2 hover:text-gray-300 rounded-md hover:bg-gray-700 transition-colors" 
                  onClick={() => toggleDropdown(category.name)}
                >
                  {category.name}
                  <svg 
                    className={`w-4 h-4 ml-1 transform ${activeDropdown === category.name ? 'rotate-180' : ''}`} 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
                
                {activeDropdown === category.name && (
                  <div className="absolute left-0 mt-1 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50">
                    <div className="py-1">
                      {category.tools.map((tool) => (
                        <Link
                          key={tool.path}
                          to={tool.path}
                          className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                          onClick={handleLinkClick}
                        >
                          {tool.name}
                        </Link>
                      ))}
                    </div>
                  </div>
                )}
              </li>
            ))}
          </ul>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="lg:hidden w-full mt-4">
            <ul className="flex flex-col space-y-2">
              <li>
                <Link 
                  to="/" 
                  className="block px-3 py-2 hover:text-gray-300 rounded-md hover:bg-gray-700 transition-colors"
                  onClick={handleLinkClick}
                >
                  Home
                </Link>
              </li>
              
              {categories.map((category) => (
                <li key={category.name}>
                  <button 
                    className="flex items-center justify-between w-full px-3 py-2 hover:text-gray-300 rounded-md hover:bg-gray-700 transition-colors" 
                    onClick={() => toggleDropdown(category.name)}
                  >
                    {category.name}
                    <svg 
                      className={`w-4 h-4 ml-1 transform ${activeDropdown === category.name ? 'rotate-180' : ''}`} 
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>
                  
                  {activeDropdown === category.name && (
                    <div className="pl-4 mt-1 border-l-2 border-gray-600 ml-2">
                      {category.tools.map((tool) => (
                        <Link
                          key={tool.path}
                          to={tool.path}
                          className="block px-4 py-2 text-sm hover:text-gray-300"
                          onClick={handleLinkClick}
                        >
                          {tool.name}
                        </Link>
                      ))}
                    </div>
                  )}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navbar;
