import React, { useState, useEffect } from 'react';
import SEO from '../components/SEO';

const PasswordGenerator = () => {
  const [password, setPassword] = useState('');
  const [strength, setStrength] = useState(null);
  const [suggestions, setSuggestions] = useState([]);
  const [generatedPassword, setGeneratedPassword] = useState('');
  const [length, setLength] = useState(12);
  const [options, setOptions] = useState({
    uppercase: true,
    lowercase: true,
    numbers: true,
    symbols: true
  });

  // Password strength checker
  useEffect(() => {
    if (!password) {
      setStrength(null);
      setSuggestions([]);
      return;
    }

    const checks = {
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      lowercase: /[a-z]/.test(password),
      numbers: /[0-9]/.test(password),
      symbols: /[^A-Za-z0-9]/.test(password)
    };

    // Calculate strength
    const score = Object.values(checks).filter(Boolean).length;
    let strengthLevel;
    let strengthColor;
    let newSuggestions = [];

    if (score <= 2) {
      strengthLevel = 'Weak';
      strengthColor = 'red';
    } else if (score <= 4) {
      strengthLevel = 'Medium';
      strengthColor = 'yellow';
    } else {
      strengthLevel = 'Strong';
      strengthColor = 'green';
    }

    // Generate suggestions
    if (!checks.length) newSuggestions.push('Make your password at least 8 characters long');
    if (!checks.uppercase) newSuggestions.push('Add uppercase letters (A-Z)');
    if (!checks.lowercase) newSuggestions.push('Add lowercase letters (a-z)');
    if (!checks.numbers) newSuggestions.push('Add numbers (0-9)');
    if (!checks.symbols) newSuggestions.push('Add special characters (!@#$%^&*)');

    setStrength({ level: strengthLevel, color: strengthColor });
    setSuggestions(newSuggestions);
  }, [password]);

  // Generate random password
  const generatePassword = () => {
    const uppercaseChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const lowercaseChars = 'abcdefghijklmnopqrstuvwxyz';
    const numberChars = '0123456789';
    const symbolChars = '!@#$%^&*()_+-=[]{}|;:,.<>?';

    let chars = '';
    if (options.uppercase) chars += uppercaseChars;
    if (options.lowercase) chars += lowercaseChars;
    if (options.numbers) chars += numberChars;
    if (options.symbols) chars += symbolChars;

    if (chars === '') {
      setSuggestions(['Please select at least one character type']);
      return;
    }

    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }

    // Ensure the generated password meets all selected criteria
    if (options.uppercase && !/[A-Z]/.test(result)) return generatePassword();
    if (options.lowercase && !/[a-z]/.test(result)) return generatePassword();
    if (options.numbers && !/[0-9]/.test(result)) return generatePassword();
    if (options.symbols && !/[^A-Za-z0-9]/.test(result)) return generatePassword();

    setGeneratedPassword(result);
    setPassword(result);
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text).then(() => {
      alert('Password copied to clipboard!');
    }).catch(err => {
      console.error('Failed to copy: ', err);
    });
  };

  return (
    <>
      <SEO 
        title="Password Generator & Strength Checker | ToollyHub"
        description="Generate strong, secure passwords and check password strength in real-time. Get instant feedback and suggestions to improve your password security. Free, easy-to-use password tool."
        keywords="password generator, password strength checker, secure password, random password generator, password security, password strength meter, password suggestions, password complexity"
      />
      <div className="container mx-auto p-6">
        <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
          <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">Password Generator</h2>

          {/* Password Strength Checker */}
          <div className="mb-6">
            <label htmlFor="password" className="block text-gray-700 font-medium mb-2">
              Check Password Strength
            </label>
            <div className="relative">
              <input
                type="text"
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter or generate a password"
              />
              {password && (
                <button
                  onClick={() => copyToClipboard(password)}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                >
                  📋
                </button>
              )}
            </div>

            {strength && (
              <div className="mt-2">
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm font-medium">Strength:</span>
                  <span className={`text-sm font-medium text-${strength.color}-600`}>
                    {strength.level}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full bg-${strength.color}-500`}
                    style={{
                      width: strength.level === 'Weak' ? '33%' : strength.level === 'Medium' ? '66%' : '100%'
                    }}
                  ></div>
                </div>
              </div>
            )}

            {suggestions.length > 0 && (
              <div className="mt-2">
                <p className="text-sm font-medium text-gray-700 mb-1">Suggestions:</p>
                <ul className="text-sm text-gray-600 list-disc pl-5">
                  {suggestions.map((suggestion, index) => (
                    <li key={index}>{suggestion}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>

          {/* Password Generator */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Generate Password</h3>
            
            <div className="mb-4">
              <label className="block text-gray-700 font-medium mb-2">Password Length: {length}</label>
              <input
                type="range"
                min="8"
                max="32"
                value={length}
                onChange={(e) => setLength(parseInt(e.target.value))}
                className="w-full"
              />
            </div>

            <div className="space-y-2 mb-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={options.uppercase}
                  onChange={(e) => setOptions({...options, uppercase: e.target.checked})}
                  className="form-checkbox h-4 w-4 text-blue-600"
                />
                <span className="ml-2">Include Uppercase (A-Z)</span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={options.lowercase}
                  onChange={(e) => setOptions({...options, lowercase: e.target.checked})}
                  className="form-checkbox h-4 w-4 text-blue-600"
                />
                <span className="ml-2">Include Lowercase (a-z)</span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={options.numbers}
                  onChange={(e) => setOptions({...options, numbers: e.target.checked})}
                  className="form-checkbox h-4 w-4 text-blue-600"
                />
                <span className="ml-2">Include Numbers (0-9)</span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={options.symbols}
                  onChange={(e) => setOptions({...options, symbols: e.target.checked})}
                  className="form-checkbox h-4 w-4 text-blue-600"
                />
                <span className="ml-2">Include Symbols (!@#$%^&*)</span>
              </label>
            </div>

            <button
              onClick={generatePassword}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Generate Password
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default PasswordGenerator; 