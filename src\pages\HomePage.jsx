import React from 'react';
import ToolCard from '../components/ToolCard'; // Adjust path if necessary
import SEO from '../components/SEO';

const HomePage = () => {
  const toolsData = [
    {
      title: "Simple Calculator",
      description: "Perform basic arithmetic operations with our easy-to-use calculator.",
      icon: "🧮",
      linkTo: "/simple-calculator",
    },
    {
      title: "Scientific Calculator",
      description: "Advanced calculator with trigonometric, logarithmic, and other scientific functions.",
      icon: "🔬",
      linkTo: "/scientific-calculator",
    },
    {
      title: "Age Calculator",
      description: "Calculate your age precisely in years, months, and days.",
      icon: "🎂",
      linkTo: "/age-calculator",
    },
    {
      title: "BMI Calculator",
      description: "Check your Body Mass Index with category and health tips.",
      icon: "⚖️",
      linkTo: "/bmi-calculator",
    },
    {
      title: "Unit Converter",
      description: "Convert various units of length, weight, and temperature.",
      icon: "🔄",
      linkTo: "/unit-converter",
    },
    {
      title: "Countdown Timer",
      description: "Set a timer for future events and watch the countdown.",
      icon: "⏱️",
      linkTo: "/countdown-timer",
    },
    {
      title: "Password Generator",
      description: "Generate secure passwords and check password strength.",
      icon: "🔐",
      linkTo: "/password-generator",
    },
    {
      title: "Color Palette Generator",
      description: "Create beautiful color palettes, lock colors, and share them with others.",
      icon: "🎨",
      linkTo: "/color-palette-generator",
    },
    {
      title: "Days Until Calculator",
      description: "Find out how many days until important events like Christmas or any custom date.",
      icon: "📅",
      linkTo: "/days-until-calculator",
    },
    {
      title: "World Clock",
      description: "Check current time in major cities around the world with this timezone tool.",
      icon: "🕓",
      linkTo: "/world-clock",
    },
    {
      title: "Percentage Calculator",
      description: "Calculate percentages, discounts, increases, and decreases quickly and easily.",
      icon: "🧮",
      linkTo: "/percentage-calculator",
    },
    {
      title: "Gradient Generator",
      description: "Create beautiful CSS gradients for your website with customizable colors and directions.",
      icon: "🌈",
      linkTo: "/gradient-generator",
    },
    {
      title: "QR Code Generator",
      description: "Generate QR codes for text, URLs, emails, phone numbers, WiFi, contacts, and events with customization options.",
      icon: "📱",
      linkTo: "/qr-code-generator",
    },
    {
      title: "Lorem Ipsum Generator",
      description: "Generate placeholder text for web design, typography, and layout projects.",
      icon: "📝",
      linkTo: "/lorem-ipsum-generator",
    },
    {
      title: "Number to Words Converter",
      description: "Convert numbers to their word representation in English, perfect for financial documents.",
      icon: "🔢",
      linkTo: "/number-to-words-converter",
    },
    {
      title: "Responsive Screen Tester",
      description: "Test how your website looks on different screen sizes and devices.",
      icon: "📱",
      linkTo: "/responsive-screen-tester",
    },
    {
      title: "Text Counter",
      description: "Count characters, words, sentences, and paragraphs in your text with detailed statistics.",
      icon: "📊",
      linkTo: "/text-counter",
    },
    {
      title: "Text Case Converter",
      description: "Convert text between different cases: uppercase, lowercase, title case, and more.",
      icon: "🔡",
      linkTo: "/text-case-converter",
    },
    {
      title: "Date Difference Calculator",
      description: "Calculate the exact difference between two dates in various time units.",
      icon: "📆",
      linkTo: "/date-difference-calculator",
    },
    // Future tools can be added here
  ];

  return (
    <>
      <SEO 
        title="ToollyHub - Free Online Tools for Everyday Calculations"
        description="ToollyHub provides free, easy-to-use online tools including BMI Calculator, Age Calculator, Unit Converter, Countdown Timer, and Password Generator. All tools are free, accurate, and designed for everyday use."
        keywords="online tools, free calculators, BMI calculator, age calculator, unit converter, countdown timer, password generator, measurement tools, calculation tools, free online utilities, tool collection"
      />
      <div className="container mx-auto p-6 min-h-screen"> {/* Added min-h-screen for better footer placement */}
        <header className="text-center my-10">
          <h1 className="text-5xl font-bold text-gray-800">
            Welcome to ToollyHub!
          </h1>
          <p className="text-xl text-gray-600 mt-4">
            Your go-to hub for smart, everyday tools.
          </p>
        </header>

        {/* Ad Placeholder 1 - After Header */}
        <div className="my-12 p-4 h-24 border-2 border-dashed border-gray-300 bg-gray-50 text-center text-gray-500 flex items-center justify-center shadow-sm rounded-md">
          <span className="text-lg">Ad Placeholder (e.g., Leaderboard 728x90)</span>
        </div>

        <main>
          <h2 className="text-3xl font-semibold text-gray-700 mb-8 text-center md:text-left">
            Our Tools
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
            {toolsData.map((tool) => (
              <ToolCard
                key={tool.title}
                title={tool.title}
                description={tool.description}
                icon={tool.icon}
                linkTo={tool.linkTo}
              />
            ))}
          </div>
        </main>

        {/* Ad Placeholder 2 - Potentially after tools or before footer */}
        <div className="my-16 p-4 h-48 border-2 border-dashed border-gray-300 bg-gray-50 text-center text-gray-500 flex items-center justify-center shadow-sm rounded-md">
          <span className="text-lg">Ad Placeholder (e.g., Medium Rectangle 300x250)</span>
        </div>
      </div>
    </>
  );
};

export default HomePage;
