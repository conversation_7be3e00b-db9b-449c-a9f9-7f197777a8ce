import React, { useState, useEffect } from 'react';
import SEO from '../components/SEO';

const WorldClock = () => {
  const [currentTime, setCurrentTime] = useState(new Date());
  
  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    
    return () => clearInterval(timer);
  }, []);
  
  // List of cities with their timezone offsets
  const cities = [
    { name: "New York", timezone: "America/New_York", offset: -4, country: "USA", emoji: "🇺🇸" },
    { name: "Los Angeles", timezone: "America/Los_Angeles", offset: -7, country: "USA", emoji: "🇺🇸" },
    { name: "London", timezone: "Europe/London", offset: 1, country: "UK", emoji: "🇬🇧" },
    { name: "Paris", timezone: "Europe/Paris", offset: 2, country: "France", emoji: "🇫🇷" },
    { name: "Berlin", timezone: "Europe/Berlin", offset: 2, country: "Germany", emoji: "🇩🇪" },
    { name: "Moscow", timezone: "Europe/Moscow", offset: 3, country: "Russia", emoji: "🇷🇺" },
    { name: "Dubai", timezone: "Asia/Dubai", offset: 4, country: "UAE", emoji: "🇦🇪" },
    { name: "Mumbai", timezone: "Asia/Kolkata", offset: 5.5, country: "India", emoji: "🇮🇳" },
    { name: "Singapore", timezone: "Asia/Singapore", offset: 8, country: "Singapore", emoji: "🇸🇬" },
    { name: "Tokyo", timezone: "Asia/Tokyo", offset: 9, country: "Japan", emoji: "🇯🇵" },
    { name: "Sydney", timezone: "Australia/Sydney", offset: 10, country: "Australia", emoji: "🇦🇺" },
    { name: "Auckland", timezone: "Pacific/Auckland", offset: 12, country: "New Zealand", emoji: "🇳🇿" },
  ];

  // Function to format time with leading zeros
  const formatTime = (time) => {
    return time < 10 ? `0${time}` : time;
  };

  // Function to get time for a specific city based on its UTC offset
  const getCityTime = (offset) => {
    const localTime = currentTime;
    
    // Get UTC time in milliseconds
    const utc = localTime.getTime() + (localTime.getTimezoneOffset() * 60000);
    
    // Create new Date object for the city using supplied offset
    const cityTime = new Date(utc + (3600000 * offset));
    
    const hours = formatTime(cityTime.getHours());
    const minutes = formatTime(cityTime.getMinutes());
    const seconds = formatTime(cityTime.getSeconds());
    
    return `${hours}:${minutes}:${seconds}`;
  };

  // Get current date in string format
  const getCurrentDate = () => {
    const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
    return currentTime.toLocaleDateString(undefined, options);
  };

  return (
    <div className="container mx-auto p-6 max-w-5xl">
      <SEO 
        title="World Clock - Current Time in Major Cities"
        description="Check the current time in major cities around the world including New York, London, Tokyo, Sydney, and more with our free world clock tool."
        keywords="world clock, international time, global time zones, current time worldwide, time zone converter, world time zones"
      />
      
      <h1 className="text-3xl font-bold text-center mb-8 text-gray-800">World Clock</h1>
      
      {/* Current local time display */}
      <div className="bg-white p-6 rounded-lg shadow-md mb-8 text-center">
        <h2 className="text-xl font-medium text-gray-700">Your Local Time</h2>
        <div className="text-5xl font-bold my-4 text-blue-600 font-mono">
          {formatTime(currentTime.getHours())}:
          {formatTime(currentTime.getMinutes())}:
          {formatTime(currentTime.getSeconds())}
        </div>
        <p className="text-gray-600">{getCurrentDate()}</p>
      </div>
      
      {/* World cities grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
        {cities.map((city) => (
          <div key={city.name} className="bg-white p-5 rounded-lg shadow-md border border-gray-100 hover:shadow-lg transition-shadow">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-lg font-medium text-gray-800">{city.name}</h3>
              <span className="text-2xl" role="img" aria-label={city.country}>{city.emoji}</span>
            </div>
            <p className="text-sm text-gray-500 mb-2">{city.country}</p>
            <div className="text-3xl font-bold text-blue-600 font-mono">
              {getCityTime(city.offset)}
            </div>
          </div>
        ))}
      </div>
      
      {/* Information section */}
      <div className="bg-white p-6 rounded-lg shadow-md mb-8">
        <h2 className="text-xl font-semibold mb-4 text-gray-700">About World Time Zones</h2>
        <p className="text-gray-600 mb-4">
          Time zones are regions on Earth that observe a uniform standard time for legal, commercial, and social purposes. 
          Time zones tend to follow the boundaries of countries and their subdivisions because it is convenient for areas in close commercial 
          or other communication to keep the same time.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
          <div>
            <h3 className="font-medium text-gray-700 mb-2">UTC and GMT</h3>
            <p className="text-gray-600 text-sm">
              Coordinated Universal Time (UTC) is the primary time standard by which the world regulates clocks and time. 
              It is similar to Greenwich Mean Time (GMT), although there are slight technical differences.
            </p>
          </div>
          <div>
            <h3 className="font-medium text-gray-700 mb-2">Daylight Saving Time</h3>
            <p className="text-gray-600 text-sm">
              Many regions observe daylight saving time (DST), which typically involves advancing clocks by one hour during summer months.
              This tool shows standard time offsets and may not reflect current DST adjustments.
            </p>
          </div>
        </div>
      </div>
      
      {/* SEO content */}
      <div className="prose prose-blue max-w-none">
        <h2>Why Use a World Clock?</h2>
        <p>
          In our globally connected world, knowing the current time in different cities around the globe is essential for:
        </p>
        <ul>
          <li>Planning international calls and video conferences</li>
          <li>Coordinating with colleagues, friends, or family in different time zones</li>
          <li>Scheduling travel and understanding jet lag impacts</li>
          <li>Following global events, sports, or financial markets</li>
        </ul>
        
        <h2>How This World Clock Works</h2>
        <p>
          Our World Clock tool uses JavaScript to calculate the current time in various cities around the world based on your device's clock and known UTC offsets.
          No server communication is needed, so times are always up-to-date and accurate.
        </p>
        
        <h3>Time Zone Calculations</h3>
        <p>
          Time zones are calculated as offsets from Coordinated Universal Time (UTC). For example, New York is typically UTC-4 or UTC-5 depending on
          daylight saving time, while Tokyo is UTC+9. These offsets allow us to know the exact time anywhere in the world at any given moment.
        </p>
        
        <p>
          Please note that this tool provides approximate times and may not account for recent changes in time zone policies or daylight saving time transitions.
          For absolute precision in official contexts, please consult official time sources for each location.
        </p>
      </div>
    </div>
  );
};

export default WorldClock;
