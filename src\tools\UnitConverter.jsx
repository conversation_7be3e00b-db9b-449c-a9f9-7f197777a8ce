import React, { useState, useEffect } from 'react';
import SEO from '../components/SEO';

const UnitConverter = () => {
  const [inputValue, setInputValue] = useState('');
  const [fromUnit, setFromUnit] = useState('meters');
  const [toUnit, setToUnit] = useState('feet');
  const [result, setResult] = useState('');
  const [category, setCategory] = useState('length'); // 'length', 'weight', 'temperature'
  const [error, setError] = useState('');

  // Unit conversion factors and options
  const unitCategories = {
    length: {
      name: 'Length',
      units: ['millimeters', 'centimeters', 'meters', 'kilometers', 'inches', 'feet', 'yards', 'miles']
    },
    weight: {
      name: 'Weight',
      units: ['milligrams', 'grams', 'kilograms', 'tonnes', 'ounces', 'pounds', 'stone', 'tons']
    },
    temperature: {
      name: 'Temperature',
      units: ['celsius', 'fahrenheit', 'kelvin']
    },
    volume: {
      name: 'Volume',
      units: ['milliliters', 'liters', 'cubic-meters', 'fluid-ounces', 'cups', 'pints', 'gallons']
    }
  };

  // Reset units when category changes
  useEffect(() => {
    const defaultUnits = unitCategories[category].units;
    setFromUnit(defaultUnits[0]);
    setToUnit(defaultUnits[1]);
    setResult('');
  }, [category]);

  const handleCategoryChange = (e) => {
    setCategory(e.target.value);
    setInputValue('');
    setResult('');
    setError('');
  };

  const convert = () => {
    if (!inputValue.trim()) {
      setError('Please enter a value to convert');
      setResult('');
      return;
    }

    if (isNaN(inputValue)) {
      setError('Please enter a valid number');
      setResult('');
      return;
    }

    setError('');
    const value = parseFloat(inputValue);
    let convertedValue;

    // Convert to base unit first (e.g., meters for length)
    let baseValue;

    // Temperature conversion needs special handling
    if (category === 'temperature') {
      convertedValue = convertTemperature(value, fromUnit, toUnit);
    } else {
      // For non-temperature units, use conversion factors
      baseValue = convertToBaseUnit(value, fromUnit, category);
      convertedValue = convertFromBaseUnit(baseValue, toUnit, category);
    }

    setResult(convertedValue.toFixed(4));
  };

  // Convert from any unit to the base unit
  const convertToBaseUnit = (value, unit, category) => {
    switch (category) {
      case 'length':
        switch (unit) {
          case 'millimeters': return value * 0.001;
          case 'centimeters': return value * 0.01;
          case 'meters': return value;
          case 'kilometers': return value * 1000;
          case 'inches': return value * 0.0254;
          case 'feet': return value * 0.3048;
          case 'yards': return value * 0.9144;
          case 'miles': return value * 1609.34;
          default: return value;
        }
      case 'weight':
        switch (unit) {
          case 'milligrams': return value * 0.000001;
          case 'grams': return value * 0.001;
          case 'kilograms': return value;
          case 'tonnes': return value * 1000;
          case 'ounces': return value * 0.0283495;
          case 'pounds': return value * 0.453592;
          case 'stone': return value * 6.35029;
          case 'tons': return value * 907.185;
          default: return value;
        }
      case 'volume':
        switch (unit) {
          case 'milliliters': return value * 0.001;
          case 'liters': return value;
          case 'cubic-meters': return value * 1000;
          case 'fluid-ounces': return value * 0.0295735;
          case 'cups': return value * 0.236588;
          case 'pints': return value * 0.473176;
          case 'gallons': return value * 3.78541;
          default: return value;
        }
      default: return value;
    }
  };

  // Convert from base unit to any unit
  const convertFromBaseUnit = (value, unit, category) => {
    switch (category) {
      case 'length':
        switch (unit) {
          case 'millimeters': return value * 1000;
          case 'centimeters': return value * 100;
          case 'meters': return value;
          case 'kilometers': return value * 0.001;
          case 'inches': return value * 39.3701;
          case 'feet': return value * 3.28084;
          case 'yards': return value * 1.09361;
          case 'miles': return value * 0.000621371;
          default: return value;
        }
      case 'weight':
        switch (unit) {
          case 'milligrams': return value * 1000000;
          case 'grams': return value * 1000;
          case 'kilograms': return value;
          case 'tonnes': return value * 0.001;
          case 'ounces': return value * 35.274;
          case 'pounds': return value * 2.20462;
          case 'stone': return value * 0.157473;
          case 'tons': return value * 0.00110231;
          default: return value;
        }
      case 'volume':
        switch (unit) {
          case 'milliliters': return value * 1000;
          case 'liters': return value;
          case 'cubic-meters': return value * 0.001;
          case 'fluid-ounces': return value * 33.814;
          case 'cups': return value * 4.22675;
          case 'pints': return value * 2.11338;
          case 'gallons': return value * 0.264172;
          default: return value;
        }
      default: return value;
    }
  };

  // Handle temperature conversion specifically
  const convertTemperature = (value, fromUnit, toUnit) => {
    // Convert to Celsius first
    let celsiusValue;
    switch (fromUnit) {
      case 'celsius':
        celsiusValue = value;
        break;
      case 'fahrenheit':
        celsiusValue = (value - 32) * (5/9);
        break;
      case 'kelvin':
        celsiusValue = value - 273.15;
        break;
      default:
        celsiusValue = value;
    }

    // Convert from Celsius to target unit
    switch (toUnit) {
      case 'celsius':
        return celsiusValue;
      case 'fahrenheit':
        return (celsiusValue * (9/5)) + 32;
      case 'kelvin':
        return celsiusValue + 273.15;
      default:
        return celsiusValue;
    }
  };

  return (
    <>
      <SEO 
        title="Unit Converter - Convert Any Unit of Measurement | ToollyHub"
        description="Free unit converter for all your measurement needs. Convert between different units of length, weight, temperature, and more. Fast, accurate, and easy to use conversion tool."
        keywords="unit converter, measurement converter, metric converter, imperial converter, length converter, weight converter, temperature converter, unit conversion tool"
      />
      <div className="container mx-auto p-6">
        <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
          <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">Unit Converter</h2>
          
          <div className="mb-4">
            <label className="block text-gray-700 font-medium mb-2">Select Category</label>
            <select
              value={category}
              onChange={handleCategoryChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {Object.keys(unitCategories).map(cat => (
                <option key={cat} value={cat}>{unitCategories[cat].name}</option>
              ))}
            </select>
          </div>
          
          <div className="mb-4">
            <label htmlFor="inputValue" className="block text-gray-700 font-medium mb-2">Value</label>
            <input
              type="text"
              id="inputValue"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder={`Enter value`}
            />
          </div>
          
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <label htmlFor="fromUnit" className="block text-gray-700 font-medium mb-2">From</label>
              <select
                id="fromUnit"
                value={fromUnit}
                onChange={(e) => setFromUnit(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {unitCategories[category].units.map(unit => (
                  <option key={unit} value={unit}>{unit}</option>
                ))}
              </select>
            </div>
            
            <div>
              <label htmlFor="toUnit" className="block text-gray-700 font-medium mb-2">To</label>
              <select
                id="toUnit"
                value={toUnit}
                onChange={(e) => setToUnit(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {unitCategories[category].units.map(unit => (
                  <option key={unit} value={unit}>{unit}</option>
                ))}
              </select>
            </div>
          </div>
          
          {error && (
            <div className="mb-4 p-2 bg-red-100 text-red-700 rounded-md">
              {error}
            </div>
          )}
          
          <button
            onClick={convert}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Convert
          </button>
          
          {result && (
            <div className="mt-6 p-4 bg-gray-100 rounded-md">
              <h3 className="text-xl font-semibold text-gray-800 mb-2">Result:</h3>
              <div className="text-center">
                <p className="text-2xl font-bold text-blue-700">
                  {inputValue} {fromUnit} = {result} {toUnit}
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default UnitConverter;
