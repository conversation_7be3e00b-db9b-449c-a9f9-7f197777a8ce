import React, { useState, useEffect } from 'react';
import SEO from '../components/SEO';

const GradientGenerator = () => {
  const [colors, setColors] = useState(['#4158D0', '#C850C0']);
  const [gradientType, setGradientType] = useState('linear');
  const [direction, setDirection] = useState('to right');
  const [cssCode, setCssCode] = useState('');
  const [copied, setCopied] = useState(false);
  const [presets, setPresets] = useState([
    { name: 'Sunset', colors: ['#FF512F', '#F09819'], type: 'linear', direction: 'to right' },
    { name: 'Ocean', colors: ['#2193b0', '#6dd5ed'], type: 'linear', direction: 'to bottom' },
    { name: 'Purple Love', colors: ['#cc2b5e', '#753a88'], type: 'linear', direction: 'to right' },
    { name: 'Summer', colors: ['#22c1c3', '#fdbb2d'], type: 'linear', direction: '135deg' },
    { name: 'Sky', colors: ['#1488CC', '#2B32B2'], type: 'linear', direction: 'to top' },
    { name: 'Rainbow', colors: ['#00F5A0', '#00D9F5', '#9E00FF'], type: 'linear', direction: 'to right' },
    { name: 'Radial Sunset', colors: ['#EB3349', '#F45C43'], type: 'radial', direction: 'circle' },
    { name: 'Cosmic Fusion', colors: ['#ff00cc', '#333399'], type: 'linear', direction: '45deg' },
  ]);
  
  // Directions for linear gradients
  const linearDirections = [
    'to right',
    'to left',
    'to bottom',
    'to top',
    'to bottom right',
    'to bottom left',
    'to top right',
    'to top left',
    '45deg',
    '135deg',
    '225deg',
    '315deg'
  ];
  
  // Shapes for radial gradients
  const radialShapes = [
    'circle',
    'ellipse'
  ];
  
  // Update CSS code when any of the gradient properties change
  useEffect(() => {
    generateCssCode();
  }, [colors, gradientType, direction]);
  
  // Generate CSS code based on current settings
  const generateCssCode = () => {
    let colorStops = '';
    
    colors.forEach((color, index) => {
      colorStops += color;
      if (index < colors.length - 1) {
        colorStops += ', ';
      }
    });
    
    let cssValue = '';
    if (gradientType === 'linear') {
      cssValue = `linear-gradient(${direction}, ${colorStops})`;
    } else if (gradientType === 'radial') {
      cssValue = `radial-gradient(${direction}, ${colorStops})`;
    }
    
    setCssCode(`background-image: ${cssValue};`);
  };
  
  // Handle color change for a specific index
  const handleColorChange = (index, value) => {
    const newColors = [...colors];
    newColors[index] = value;
    setColors(newColors);
  };
  
  // Add a new color
  const addColor = () => {
    if (colors.length < 5) { // Limit to 5 colors for simplicity
      // Generate a random color
      const randomColor = '#' + Math.floor(Math.random()*16777215).toString(16);
      setColors([...colors, randomColor]);
    }
  };
  
  // Remove a color at specific index
  const removeColor = (index) => {
    if (colors.length > 2) { // Minimum 2 colors required for gradient
      const newColors = [...colors];
      newColors.splice(index, 1);
      setColors(newColors);
    }
  };
  
  // Copy CSS code to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(cssCode).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    });
  };
  
  // Apply a preset
  const applyPreset = (preset) => {
    setColors(preset.colors);
    setGradientType(preset.type);
    setDirection(preset.direction);
  };
  
  // Save current gradient as a preset
  const saveAsPreset = () => {
    const presetName = prompt('Enter a name for this preset:');
    if (presetName) {
      const newPreset = {
        name: presetName,
        colors: [...colors],
        type: gradientType,
        direction: direction
      };
      setPresets([...presets, newPreset]);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-5xl">
      <SEO 
        title="CSS Gradient Generator - Create Beautiful Gradients"
        description="Create stunning CSS gradients for your website with our easy-to-use gradient generator. Choose colors, direction, and type to generate ready-to-use CSS code."
        keywords="gradient generator, CSS gradient, background gradient, web design tool, color gradient, linear gradient, radial gradient, gradient CSS code"
      />
      
      <h1 className="text-3xl font-bold text-center mb-8 text-gray-800">CSS Gradient Generator</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Controls Section */}
        <div className="lg:col-span-1 bg-white p-6 rounded-lg shadow-md space-y-6">
          <h2 className="text-xl font-semibold mb-4 text-gray-700">Gradient Settings</h2>
          
          {/* Gradient Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Gradient Type</label>
            <div className="flex space-x-4">
              <button
                className={`px-4 py-2 rounded-md ${gradientType === 'linear' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-800'}`}
                onClick={() => setGradientType('linear')}
              >
                Linear
              </button>
              <button
                className={`px-4 py-2 rounded-md ${gradientType === 'radial' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-800'}`}
                onClick={() => setGradientType('radial')}
              >
                Radial
              </button>
            </div>
          </div>
          
          {/* Direction/Shape */}
          <div>
            <label htmlFor="direction" className="block text-sm font-medium text-gray-700 mb-2">
              {gradientType === 'linear' ? 'Direction' : 'Shape'}
            </label>
            <select
              id="direction"
              value={direction}
              onChange={(e) => setDirection(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {gradientType === 'linear' ? (
                linearDirections.map((dir) => (
                  <option key={dir} value={dir}>
                    {dir}
                  </option>
                ))
              ) : (
                radialShapes.map((shape) => (
                  <option key={shape} value={shape}>
                    {shape}
                  </option>
                ))
              )}
            </select>
          </div>
          
          {/* Color Stops */}
          <div>
            <div className="flex justify-between items-center mb-2">
              <label className="block text-sm font-medium text-gray-700">Colors</label>
              {colors.length < 5 && (
                <button 
                  onClick={addColor}
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  + Add Color
                </button>
              )}
            </div>
            
            {colors.map((color, index) => (
              <div key={index} className="flex items-center mb-3">
                <input
                  type="color"
                  value={color}
                  onChange={(e) => handleColorChange(index, e.target.value)}
                  className="w-10 h-10 rounded-md border border-gray-300 mr-3"
                />
                <input
                  type="text"
                  value={color}
                  onChange={(e) => handleColorChange(index, e.target.value)}
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                {colors.length > 2 && (
                  <button
                    onClick={() => removeColor(index)}
                    className="ml-2 text-red-600 hover:text-red-800"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </button>
                )}
              </div>
            ))}
          </div>
          
          {/* Save as Preset */}
          <button
            onClick={saveAsPreset}
            className="w-full px-4 py-2 border border-blue-600 text-blue-600 rounded-md hover:bg-blue-50 transition-colors"
          >
            Save as Preset
          </button>
        </div>
        
        {/* Preview and Code Section */}
        <div className="lg:col-span-2 space-y-6">
          {/* Gradient Preview */}
          <div 
            className="h-64 rounded-lg shadow-md flex items-center justify-center"
            style={{ backgroundImage: gradientType === 'linear' 
              ? `linear-gradient(${direction}, ${colors.join(', ')})` 
              : `radial-gradient(${direction}, ${colors.join(', ')})` 
            }}
          >
            <span className="px-4 py-2 bg-white/80 backdrop-blur-sm rounded-lg font-medium text-gray-800">
              Preview
            </span>
          </div>
          
          {/* CSS Code */}
          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-700">CSS Code</h3>
              <button
                onClick={copyToClipboard}
                className="px-4 py-1 text-sm bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
              >
                {copied ? 'Copied!' : 'Copy'}
              </button>
            </div>
            <div className="bg-gray-50 p-4 rounded-md font-mono text-sm overflow-x-auto">
              {cssCode}
            </div>
          </div>
          
          {/* Presets */}
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="text-lg font-medium text-gray-700 mb-4">Presets</h3>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
              {presets.map((preset, index) => (
                <div
                  key={index}
                  onClick={() => applyPreset(preset)}
                  className="h-16 rounded-md shadow-sm cursor-pointer hover:shadow-md transition-shadow"
                  style={{
                    backgroundImage: preset.type === 'linear'
                      ? `linear-gradient(${preset.direction}, ${preset.colors.join(', ')})`
                      : `radial-gradient(${preset.direction}, ${preset.colors.join(', ')})`
                  }}
                  title={preset.name}
                >
                  <div className="h-full w-full flex items-end justify-center p-1">
                    <span className="text-xs bg-white/80 backdrop-blur-sm px-2 py-1 rounded text-gray-800 truncate max-w-full">
                      {preset.name}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
      
      {/* SEO Content */}
      <div className="mt-12 prose prose-blue max-w-none">
        <h2>Using CSS Gradients in Web Design</h2>
        <p>
          CSS gradients allow you to display smooth transitions between two or more specified colors. They are a popular way to add depth, 
          visual interest, and modern aesthetics to websites without using image files.
        </p>
        
        <h3>Types of CSS Gradients</h3>
        <p>
          There are two main types of CSS gradients:
        </p>
        
        <h4>Linear Gradients</h4>
        <p>
          Linear gradients transition colors along a straight line. You can control the direction of this line using keywords 
          (like "to right" or "to bottom") or specific angles (like "45deg" or "90deg").
        </p>
        
        <h4>Radial Gradients</h4>
        <p>
          Radial gradients transition colors outward from a central point in a circular or elliptical pattern. 
          You can specify whether you want a circle or ellipse and control its position.
        </p>
        
        <h3>Benefits of Using CSS Gradients</h3>
        <ul>
          <li><strong>Performance:</strong> CSS gradients are rendered by the browser, so they load faster than image files.</li>
          <li><strong>Scalability:</strong> They scale perfectly to any size without losing quality.</li>
          <li><strong>Customization:</strong> Easy to modify by changing colors, directions, or adding color stops.</li>
          <li><strong>Reduced HTTP Requests:</strong> No need to load external image files.</li>
          <li><strong>Accessibility:</strong> Text over properly designed gradients can remain readable and accessible.</li>
        </ul>
        
        <h3>Popular Uses for Gradients in Web Design</h3>
        <ul>
          <li>Background elements for headers, hero sections, or entire pages</li>
          <li>Button styling to create depth and visual interest</li>
          <li>Call-to-action elements that draw attention</li>
          <li>Separators between content sections</li>
          <li>Card backgrounds for a modern, visually appealing design</li>
          <li>Text effects (with careful implementation for accessibility)</li>
        </ul>
        
        <h3>Tips for Creating Effective Gradients</h3>
        <ol>
          <li>Choose colors that complement your brand identity and design palette</li>
          <li>Consider contrast for text readability if placing content over gradients</li>
          <li>Use subtle gradients for sophisticated designs, bold ones for dramatic impact</li>
          <li>Experiment with different directions and angles for unique effects</li>
          <li>Consider using gradients with transparency to overlay on images or other content</li>
        </ol>
        
        <p>
          With our Gradient Generator tool, you can easily create, preview, and get the exact CSS code for any gradient design you envision. 
          Simply select your colors, choose a gradient type and direction, then copy the generated code directly into your CSS.
        </p>
      </div>
    </div>
  );
};

export default GradientGenerator;
