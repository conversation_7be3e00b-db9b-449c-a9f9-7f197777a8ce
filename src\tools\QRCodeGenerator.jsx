import React, { useState, useEffect, useRef } from 'react';
import QRCode from 'qrcode';
import SEO from '../components/SEO';

const QRCodeGenerator = () => {
  const [qrType, setQrType] = useState('text');
  const [qrData, setQrData] = useState('');
  const [qrOptions, setQrOptions] = useState({
    size: 256,
    margin: 4,
    color: {
      dark: '#000000',
      light: '#FFFFFF'
    },
    errorCorrectionLevel: 'M'
  });
  const [generatedQR, setGeneratedQR] = useState('');
  const [history, setHistory] = useState([]);
  const [showCopied, setShowCopied] = useState(false);
  const canvasRef = useRef(null);

  // QR Code types configuration
  const qrTypes = {
    text: { label: 'Text', icon: '📝' },
    url: { label: 'URL', icon: '🔗' },
    email: { label: 'Email', icon: '📧' },
    phone: { label: 'Phone', icon: '📞' },
    sms: { label: 'SMS', icon: '💬' },
    wifi: { label: 'WiFi', icon: '📶' },
    vcard: { label: 'Contact', icon: '👤' },
    event: { label: 'Event', icon: '📅' }
  };

  // Form data state for different QR types
  const [formData, setFormData] = useState({
    text: { content: '' },
    url: { url: '' },
    email: { email: '', subject: '', body: '' },
    phone: { number: '' },
    sms: { number: '', message: '' },
    wifi: { ssid: '', password: '', security: 'WPA', hidden: false },
    vcard: {
      firstName: '', lastName: '', organization: '', phone: '',
      email: '', website: '', address: ''
    },
    event: {
      title: '', location: '', description: '',
      startDate: '', endDate: '', startTime: '', endTime: ''
    }
  });

  // Load history from localStorage
  useEffect(() => {
    const savedHistory = localStorage.getItem('qrHistory');
    if (savedHistory) {
      setHistory(JSON.parse(savedHistory));
    }
  }, []);

  // Save history to localStorage
  useEffect(() => {
    localStorage.setItem('qrHistory', JSON.stringify(history));
  }, [history]);

  // Auto-generate QR code when form data changes
  useEffect(() => {
    if (!canvasRef.current) return; // Wait for canvas to be ready

    const timeoutId = setTimeout(() => {
      generateQR(false); // Don't save to history on auto-generation
    }, 300); // Debounce for 300ms

    return () => clearTimeout(timeoutId);
  }, [formData, qrType]);

  // Regenerate QR code when options change
  useEffect(() => {
    if (generatedQR && canvasRef.current) {
      generateQR(false);
    }
  }, [qrOptions]);

  // Generate initial QR code with sample data
  useEffect(() => {
    // Set initial sample data for all types
    setFormData(prev => ({
      ...prev,
      text: { content: 'Hello World! This is a sample QR code.' },
      url: { url: 'https://example.com' },
      email: { email: '<EMAIL>', subject: 'Hello', body: 'Sample message' },
      phone: { number: '+1234567890' },
      sms: { number: '+1234567890', message: 'Hello from QR code!' },
      wifi: { ssid: 'MyWiFi', password: 'password123', security: 'WPA', hidden: false },
      vcard: {
        firstName: 'John', lastName: 'Doe', organization: 'Company Inc',
        phone: '+1234567890', email: '<EMAIL>', website: 'https://company.com',
        address: '123 Main St, City, State'
      },
      event: {
        title: 'Sample Event', location: 'Conference Room', description: 'Important meeting',
        startDate: '2024-12-01', endDate: '2024-12-01', startTime: '10:00', endTime: '11:00'
      }
    }));
  }, []);

  // Generate initial QR code after component mounts
  useEffect(() => {
    const initializeQR = () => {
      if (canvasRef.current && formData.text.content) {
        generateQR(false);
      } else {
        // Retry after a short delay if canvas isn't ready
        setTimeout(initializeQR, 100);
      }
    };

    const timer = setTimeout(initializeQR, 100);
    return () => clearTimeout(timer);
  }, []); // Run only once on mount

  // Generate QR data string based on type
  const generateQRData = () => {
    const data = formData[qrType];

    switch (qrType) {
      case 'text':
        return data.content;

      case 'url':
        return data.url.startsWith('http') ? data.url : `https://${data.url}`;

      case 'email':
        return `mailto:${data.email}?subject=${encodeURIComponent(data.subject)}&body=${encodeURIComponent(data.body)}`;

      case 'phone':
        return `tel:${data.number}`;

      case 'sms':
        return `sms:${data.number}?body=${encodeURIComponent(data.message)}`;

      case 'wifi':
        return `WIFI:T:${data.security};S:${data.ssid};P:${data.password};H:${data.hidden ? 'true' : 'false'};;`;

      case 'vcard':
        return `BEGIN:VCARD
VERSION:3.0
FN:${data.firstName} ${data.lastName}
ORG:${data.organization}
TEL:${data.phone}
EMAIL:${data.email}
URL:${data.website}
ADR:;;${data.address};;;;
END:VCARD`;

      case 'event':
        const startDateTime = `${data.startDate}T${data.startTime}:00`;
        const endDateTime = `${data.endDate}T${data.endTime}:00`;
        return `BEGIN:VEVENT
SUMMARY:${data.title}
LOCATION:${data.location}
DESCRIPTION:${data.description}
DTSTART:${startDateTime.replace(/[-:]/g, '')}
DTEND:${endDateTime.replace(/[-:]/g, '')}
END:VEVENT`;

      default:
        return '';
    }
  };

  // Generate QR Code
  const generateQR = (saveToHistory = false) => {
    const data = generateQRData();

    if (!data.trim()) {
      setGeneratedQR('');
      setQrData('');
      return;
    }

    try {
      const canvas = canvasRef.current;
      if (!canvas) {
        return;
      }

      // Set canvas size to match QR options
      canvas.width = qrOptions.size;
      canvas.height = qrOptions.size;

      QRCode.toCanvas(canvas, data, {
        ...qrOptions,
        width: qrOptions.size,
        height: qrOptions.size
      }, (error) => {
        if (error) {
          console.error('QR generation error:', error);
        } else {
          const dataURL = canvas.toDataURL();
          setGeneratedQR(dataURL);
          setQrData(data);

          // Add to history only when explicitly requested (button click)
          if (saveToHistory) {
            const historyItem = {
              id: Date.now(),
              type: qrType,
              data: data,
              qrCode: dataURL,
              timestamp: new Date().toISOString()
            };
            setHistory(prev => [historyItem, ...prev.slice(0, 9)]); // Keep last 10
          }
        }
      });
    } catch (error) {
      console.error('Error generating QR code:', error);
    }
  };

  // Update form data
  const updateFormData = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [qrType]: {
        ...prev[qrType],
        [field]: value
      }
    }));
  };

  // Download QR Code
  const downloadQR = () => {
    if (!generatedQR) return;

    const link = document.createElement('a');
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-');
    link.download = `qr-${qrType}-${timestamp}.png`;
    link.href = generatedQR;
    link.click();
  };

  // Copy QR Code to clipboard
  const copyQRToClipboard = async () => {
    if (!generatedQR) return;

    try {
      const canvas = canvasRef.current;
      canvas.toBlob(async (blob) => {
        await navigator.clipboard.write([
          new ClipboardItem({ 'image/png': blob })
        ]);
        setShowCopied(true);
        setTimeout(() => setShowCopied(false), 2000);
      });
    } catch (error) {
      console.error('Failed to copy QR code:', error);
    }
  };

  // Copy data to clipboard
  const copyDataToClipboard = async () => {
    if (!qrData) return;

    try {
      await navigator.clipboard.writeText(qrData);
      setShowCopied(true);
      setTimeout(() => setShowCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy data:', error);
    }
  };

  return (
    <>
      <SEO
        title="QR Code Generator - Create QR Codes for Text, URL, Email & More | ToollyHub"
        description="Generate QR codes for text, URLs, emails, phone numbers, WiFi, contacts, and events. Customize colors, size, and download high-quality QR codes instantly."
        keywords="QR code generator, QR code creator, generate QR code, QR code maker, text to QR, URL QR code, email QR code, WiFi QR code, contact QR code, event QR code"
      />
      <div className="container mx-auto p-6">
        <div className="max-w-4xl mx-auto bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
          <div className="bg-gradient-to-r from-indigo-500 to-indigo-600 p-6">
            <h2 className="text-2xl font-bold text-white">QR Code Generator</h2>
            <p className="text-indigo-100 mt-1">Create QR codes for any content type</p>
          </div>

          <div className="p-6">
            {/* QR Type Selection - Tab Style */}
            <div className="mb-6">
              <div className="border-b border-gray-200">
                <nav className="-mb-px flex space-x-8 overflow-x-auto">
                  {Object.entries(qrTypes).map(([type, config]) => (
                    <button
                      key={type}
                      onClick={() => setQrType(type)}
                      className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                        qrType === type
                          ? 'border-indigo-500 text-indigo-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      <span className="mr-2">{config.icon}</span>
                      {config.label}
                    </button>
                  ))}
                </nav>
              </div>
            </div>

            {/* Main Content Layout */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Left Side - Form and Controls */}
              <div className="space-y-6">

                {/* Dynamic Form Fields */}
                <div className="space-y-4">
                  {qrType === 'text' && (
                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-gray-700">Text Content</label>
                      <textarea
                        value={formData.text.content}
                        onChange={(e) => updateFormData('content', e.target.value)}
                        className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition"
                        placeholder="Enter your text here..."
                        rows="4"
                      />
                    </div>
                  )}

                  {qrType === 'url' && (
                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-gray-700">Website URL</label>
                      <input
                        type="url"
                        value={formData.url.url}
                        onChange={(e) => updateFormData('url', e.target.value)}
                        className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition"
                        placeholder="https://example.com"
                      />
                    </div>
                  )}

              {qrType === 'email' && (
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Email Address</label>
                    <input
                      type="email"
                      value={formData.email.email}
                      onChange={(e) => updateFormData('email', e.target.value)}
                      className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition"
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Subject</label>
                    <input
                      type="text"
                      value={formData.email.subject}
                      onChange={(e) => updateFormData('subject', e.target.value)}
                      className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition"
                      placeholder="Email subject"
                    />
                  </div>
                  <div className="space-y-2 sm:col-span-2">
                    <label className="block text-sm font-medium text-gray-700">Message Body</label>
                    <textarea
                      value={formData.email.body}
                      onChange={(e) => updateFormData('body', e.target.value)}
                      className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition"
                      placeholder="Email message..."
                      rows="3"
                    />
                  </div>
                </div>
              )}

              {qrType === 'phone' && (
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Phone Number</label>
                  <input
                    type="tel"
                    value={formData.phone.number}
                    onChange={(e) => updateFormData('number', e.target.value)}
                    className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition"
                    placeholder="+1234567890"
                  />
                </div>
              )}

              {qrType === 'sms' && (
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Phone Number</label>
                    <input
                      type="tel"
                      value={formData.sms.number}
                      onChange={(e) => updateFormData('number', e.target.value)}
                      className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition"
                      placeholder="+1234567890"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Message</label>
                    <textarea
                      value={formData.sms.message}
                      onChange={(e) => updateFormData('message', e.target.value)}
                      className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition"
                      placeholder="SMS message..."
                      rows="3"
                    />
                  </div>
                </div>
              )}

              {qrType === 'wifi' && (
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Network Name (SSID)</label>
                    <input
                      type="text"
                      value={formData.wifi.ssid}
                      onChange={(e) => updateFormData('ssid', e.target.value)}
                      className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition"
                      placeholder="WiFi Network Name"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Password</label>
                    <input
                      type="password"
                      value={formData.wifi.password}
                      onChange={(e) => updateFormData('password', e.target.value)}
                      className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition"
                      placeholder="WiFi Password"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Security Type</label>
                    <select
                      value={formData.wifi.security}
                      onChange={(e) => updateFormData('security', e.target.value)}
                      className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition"
                    >
                      <option value="WPA">WPA/WPA2</option>
                      <option value="WEP">WEP</option>
                      <option value="nopass">No Password</option>
                    </select>
                  </div>
                  <div className="space-y-2 flex items-center">
                    <label className="inline-flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.wifi.hidden}
                        onChange={(e) => updateFormData('hidden', e.target.checked)}
                        className="form-checkbox h-4 w-4 text-indigo-600"
                      />
                      <span className="ml-2 text-sm text-gray-700">Hidden Network</span>
                    </label>
                  </div>
                </div>
              )}

              {qrType === 'vcard' && (
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">First Name</label>
                    <input
                      type="text"
                      value={formData.vcard.firstName}
                      onChange={(e) => updateFormData('firstName', e.target.value)}
                      className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition"
                      placeholder="John"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Last Name</label>
                    <input
                      type="text"
                      value={formData.vcard.lastName}
                      onChange={(e) => updateFormData('lastName', e.target.value)}
                      className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition"
                      placeholder="Doe"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Organization</label>
                    <input
                      type="text"
                      value={formData.vcard.organization}
                      onChange={(e) => updateFormData('organization', e.target.value)}
                      className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition"
                      placeholder="Company Name"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Phone</label>
                    <input
                      type="tel"
                      value={formData.vcard.phone}
                      onChange={(e) => updateFormData('phone', e.target.value)}
                      className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition"
                      placeholder="+1234567890"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Email</label>
                    <input
                      type="email"
                      value={formData.vcard.email}
                      onChange={(e) => updateFormData('email', e.target.value)}
                      className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition"
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Website</label>
                    <input
                      type="url"
                      value={formData.vcard.website}
                      onChange={(e) => updateFormData('website', e.target.value)}
                      className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition"
                      placeholder="https://example.com"
                    />
                  </div>
                  <div className="space-y-2 sm:col-span-2">
                    <label className="block text-sm font-medium text-gray-700">Address</label>
                    <input
                      type="text"
                      value={formData.vcard.address}
                      onChange={(e) => updateFormData('address', e.target.value)}
                      className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition"
                      placeholder="123 Main St, City, State, ZIP"
                    />
                  </div>
                </div>
              )}

              {qrType === 'event' && (
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2 sm:col-span-2">
                    <label className="block text-sm font-medium text-gray-700">Event Title</label>
                    <input
                      type="text"
                      value={formData.event.title}
                      onChange={(e) => updateFormData('title', e.target.value)}
                      className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition"
                      placeholder="Event Title"
                    />
                  </div>
                  <div className="space-y-2 sm:col-span-2">
                    <label className="block text-sm font-medium text-gray-700">Location</label>
                    <input
                      type="text"
                      value={formData.event.location}
                      onChange={(e) => updateFormData('location', e.target.value)}
                      className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition"
                      placeholder="Event Location"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Start Date</label>
                    <input
                      type="date"
                      value={formData.event.startDate}
                      onChange={(e) => updateFormData('startDate', e.target.value)}
                      className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Start Time</label>
                    <input
                      type="time"
                      value={formData.event.startTime}
                      onChange={(e) => updateFormData('startTime', e.target.value)}
                      className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">End Date</label>
                    <input
                      type="date"
                      value={formData.event.endDate}
                      onChange={(e) => updateFormData('endDate', e.target.value)}
                      className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">End Time</label>
                    <input
                      type="time"
                      value={formData.event.endTime}
                      onChange={(e) => updateFormData('endTime', e.target.value)}
                      className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition"
                    />
                  </div>
                  <div className="space-y-2 sm:col-span-2">
                    <label className="block text-sm font-medium text-gray-700">Description</label>
                    <textarea
                      value={formData.event.description}
                      onChange={(e) => updateFormData('description', e.target.value)}
                      className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition"
                      placeholder="Event description..."
                      rows="3"
                    />
                  </div>
                </div>
                )}

                {/* Generate/Save Buttons */}
                <div className="flex gap-4">
                  <button
                    onClick={() => generateQR(false)}
                    className="flex-1 px-6 py-3 bg-gradient-to-r from-indigo-500 to-indigo-600 text-white font-semibold rounded-lg hover:from-indigo-600 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transform transition-all duration-200 hover:scale-[1.02]"
                  >
                    🔄 Generate QR Code
                  </button>
                  {generatedQR && (
                    <button
                      onClick={() => generateQR(true)}
                      className="flex-1 px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white font-semibold rounded-lg hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transform transition-all duration-200 hover:scale-[1.02]"
                    >
                      💾 Save to History
                    </button>
                  )}
                </div>
              </div>

              {/* Right Side - QR Preview and Customization */}
              <div className="space-y-6">
                {/* QR Code Display */}
                <div className="bg-gray-50 p-6 rounded-lg border border-gray-100 text-center">
                  <h3 className="text-lg font-semibold text-gray-800 mb-4">QR Code Preview</h3>
                  <div className="min-h-[280px] flex flex-col justify-center items-center">
                    <canvas
                      ref={canvasRef}
                      className={`mb-4 ${generatedQR ? 'block' : 'hidden'}`}
                      style={{
                        maxWidth: '100%',
                        height: 'auto',
                        border: '1px solid #e5e7eb',
                        borderRadius: '8px',
                        backgroundColor: 'white'
                      }}
                    />

                    {generatedQR ? (
                      <div className="flex flex-wrap justify-center gap-3">
                        <button
                          onClick={downloadQR}
                          className="px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg hover:from-green-600 hover:to-green-700 transition-all duration-200 font-semibold shadow-lg transform hover:scale-105"
                        >
                          📥 Download QR Code
                        </button>
                        <button
                          onClick={copyQRToClipboard}
                          className="px-4 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-medium"
                        >
                          📋 Copy
                        </button>
                        <button
                          onClick={copyDataToClipboard}
                          className="px-4 py-3 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors font-medium"
                        >
                          📄 Data
                        </button>
                      </div>
                    ) : (
                      <div className="text-gray-400">
                        <div className="text-6xl mb-4">📱</div>
                        <p className="text-lg">QR code will appear here</p>
                        <p className="text-sm mt-2">Start typing to generate automatically</p>
                      </div>
                    )}

                    {showCopied && (
                      <div className="mt-3 text-green-600 font-medium">
                        ✅ Copied to clipboard!
                      </div>
                    )}
                  </div>
                </div>

                {/* Customization Options */}
                <div className="bg-gray-50 p-6 rounded-lg border border-gray-100">
                  <h3 className="text-lg font-semibold text-gray-800 mb-4">Customize QR Code</h3>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-gray-700">
                        Size: {qrOptions.size}px
                      </label>
                      <input
                        type="range"
                        min="128"
                        max="512"
                        step="32"
                        value={qrOptions.size}
                        onChange={(e) => setQrOptions(prev => ({ ...prev, size: parseInt(e.target.value) }))}
                        className="w-full"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">Foreground</label>
                        <input
                          type="color"
                          value={qrOptions.color.dark}
                          onChange={(e) => setQrOptions(prev => ({
                            ...prev,
                            color: { ...prev.color, dark: e.target.value }
                          }))}
                          className="w-full h-10 rounded border border-gray-200"
                        />
                      </div>
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">Background</label>
                        <input
                          type="color"
                          value={qrOptions.color.light}
                          onChange={(e) => setQrOptions(prev => ({
                            ...prev,
                            color: { ...prev.color, light: e.target.value }
                          }))}
                          className="w-full h-10 rounded border border-gray-200"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-gray-700">Error Correction</label>
                      <select
                        value={qrOptions.errorCorrectionLevel}
                        onChange={(e) => setQrOptions(prev => ({ ...prev, errorCorrectionLevel: e.target.value }))}
                        className="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                      >
                        <option value="L">Low (7%)</option>
                        <option value="M">Medium (15%)</option>
                        <option value="Q">Quartile (25%)</option>
                        <option value="H">High (30%)</option>
                      </select>
                    </div>

                    {generatedQR && (
                      <div className="pt-2 border-t border-gray-200">
                        <div className="text-xs text-gray-600">
                          <strong>Data:</strong> {qrData?.length > 50 ? qrData.substring(0, 50) + '...' : qrData}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
            </div>

            {/* History Section */}
            {history.length > 0 && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-800">Recent QR Codes</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  {history.slice(0, 6).map((item) => (
                    <div key={item.id} className="bg-gray-50 p-4 rounded-lg border border-gray-100">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-700 capitalize">
                          {qrTypes[item.type]?.icon} {qrTypes[item.type]?.label}
                        </span>
                        <span className="text-xs text-gray-500">
                          {new Date(item.timestamp).toLocaleDateString()}
                        </span>
                      </div>
                      <img
                        src={item.qrCode}
                        alt="QR Code"
                        className="w-full h-24 object-contain bg-white rounded border mb-2"
                      />
                      <div className="text-xs text-gray-600 truncate mb-2" title={item.data}>
                        {item.data}
                      </div>
                      <div className="flex gap-2">
                        <button
                          onClick={() => {
                            const link = document.createElement('a');
                            link.download = `qr-${item.type}-${item.id}.png`;
                            link.href = item.qrCode;
                            link.click();
                          }}
                          className="flex-1 px-2 py-1 bg-green-500 text-white rounded text-xs hover:bg-green-600 transition-colors"
                        >
                          📥
                        </button>
                        <button
                          onClick={async () => {
                            try {
                              const response = await fetch(item.qrCode);
                              const blob = await response.blob();
                              await navigator.clipboard.write([
                                new ClipboardItem({ 'image/png': blob })
                              ]);
                              setShowCopied(true);
                              setTimeout(() => setShowCopied(false), 2000);
                            } catch (error) {
                              console.error('Failed to copy:', error);
                            }
                          }}
                          className="flex-1 px-2 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600 transition-colors"
                        >
                          📋
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
                {history.length > 6 && (
                  <button
                    onClick={() => setHistory([])}
                    className="w-full px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors text-sm font-medium"
                  >
                    🗑️ Clear History
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default QRCodeGenerator;
