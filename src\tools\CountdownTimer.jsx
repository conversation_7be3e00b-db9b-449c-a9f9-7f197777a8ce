import React, { useState, useEffect, useRef } from 'react';
import SEO from '../components/SEO';

const CountdownTimer = () => {
  const [hours, setHours] = useState(0);
  const [minutes, setMinutes] = useState(0);
  const [seconds, setSeconds] = useState(0);
  const [isRunning, setIsRunning] = useState(false);
  const [timeLeft, setTimeLeft] = useState(0); // in seconds
  const [error, setError] = useState('');
  const [isPaused, setIsPaused] = useState(false);
  const [initialTime, setInitialTime] = useState(0);
  const timerRef = useRef(null);
  const [showCompletionMessage, setShowCompletionMessage] = useState(false);

  // Sound effects for timer completion
  const audioRef = useRef(null);

  useEffect(() => {
    if (isRunning && !isPaused && timeLeft > 0) {
      timerRef.current = setInterval(() => {
        setTimeLeft(prevTime => {
          if (prevTime <= 1) {
            clearInterval(timerRef.current);
            setIsRunning(false);
            setShowCompletionMessage(true);
            // Play sound when timer completes
            if (audioRef.current) {
              audioRef.current.play().catch(e => console.error("Audio play failed:", e));
            }
            return 0;
          }
          return prevTime - 1;
        });
      }, 1000);
    }

    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
    };
  }, [isRunning, isPaused, timeLeft]);

  useEffect(() => {
    // Convert timeLeft back to hours, minutes, seconds for display
    if (!isRunning || isPaused) return;

    const h = Math.floor(timeLeft / 3600);
    const m = Math.floor((timeLeft % 3600) / 60);
    const s = timeLeft % 60;

    document.title = `${formatTime(h)}:${formatTime(m)}:${formatTime(s)} - Timer`;

    return () => {
      document.title = 'ToollyHub'; // Reset title when component unmounts or timer stops
    };
  }, [timeLeft, isRunning, isPaused]);

  const startTimer = () => {
    // Validate input
    if (hours === 0 && minutes === 0 && seconds === 0) {
      setError('Please set a time greater than zero');
      return;
    }

    setError('');
    const totalSeconds = (hours * 3600) + (minutes * 60) + seconds;
    setTimeLeft(totalSeconds);
    setInitialTime(totalSeconds);
    setIsRunning(true);
    setIsPaused(false);
    setShowCompletionMessage(false);
  };

  const pauseTimer = () => {
    setIsPaused(true);
    clearInterval(timerRef.current);
  };

  const resumeTimer = () => {
    setIsPaused(false);
  };

  const resetTimer = () => {
    clearInterval(timerRef.current);
    setIsRunning(false);
    setIsPaused(false);
    setShowCompletionMessage(false);
    setTimeLeft(0);
    setHours(0);
    setMinutes(0);
    setSeconds(0);
    setError('');
    document.title = 'ToollyHub';
  };

  const formatTime = (time) => {
    return time.toString().padStart(2, '0');
  };

  const calculateProgress = () => {
    if (initialTime === 0) return 0;
    return ((initialTime - timeLeft) / initialTime) * 100;
  };

  const displayTimeLeft = () => {
    const h = Math.floor(timeLeft / 3600);
    const m = Math.floor((timeLeft % 3600) / 60);
    const s = timeLeft % 60;

    return `${formatTime(h)}:${formatTime(m)}:${formatTime(s)}`;
  };

  return (
    <>
      <SEO 
        title="Countdown Timer - Track Time to Your Events | ToollyHub"
        description="Create custom countdown timers for any event. Track time remaining to important dates, deadlines, or special occasions. Simple, customizable, and easy to use countdown tool."
        keywords="countdown timer, event countdown, deadline timer, countdown clock, time tracker, event timer, countdown calculator, time remaining calculator"
      />
      <div className="container mx-auto p-6">
        <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
          <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">Countdown Timer</h2>
          
          {!isRunning ? (
            <div className="mb-6">
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label htmlFor="hours" className="block text-gray-700 font-medium mb-2">Hours</label>
                  <input
                    type="number"
                    id="hours"
                    min="0"
                    max="23"
                    value={hours}
                    onChange={(e) => setHours(parseInt(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label htmlFor="minutes" className="block text-gray-700 font-medium mb-2">Minutes</label>
                  <input
                    type="number"
                    id="minutes"
                    min="0"
                    max="59"
                    value={minutes}
                    onChange={(e) => setMinutes(parseInt(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label htmlFor="seconds" className="block text-gray-700 font-medium mb-2">Seconds</label>
                  <input
                    type="number"
                    id="seconds"
                    min="0"
                    max="59"
                    value={seconds}
                    onChange={(e) => setSeconds(parseInt(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            </div>
          ) : (
            <div className="mb-6">
              <div className="relative pt-1">
                <div className="flex mb-2 items-center justify-between">
                  <div>
                    <span className="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-blue-600 bg-blue-200">
                      Progress
                    </span>
                  </div>
                  <div className="text-right">
                    <span className="text-xs font-semibold inline-block text-blue-600">
                      {Math.round(calculateProgress())}%
                    </span>
                  </div>
                </div>
                <div className="overflow-hidden h-2 mb-4 text-xs flex rounded bg-blue-200">
                  <div
                    style={{ width: `${calculateProgress()}%` }}
                    className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-blue-600 transition-all duration-500"
                  ></div>
                </div>
              </div>
              <div className="text-center mb-4">
                <span className="text-5xl font-bold text-gray-800">
                  {displayTimeLeft()}
                </span>
              </div>
            </div>
          )}
          
          {error && (
            <div className="mb-4 p-2 bg-red-100 text-red-700 rounded-md">
              {error}
            </div>
          )}

          {showCompletionMessage && (
            <div className="mb-4 p-2 bg-green-100 text-green-700 rounded-md text-center">
              Time's up! Your countdown has finished.
            </div>
          )}
          
          <div className="flex justify-center space-x-4">
            {!isRunning ? (
              <button
                onClick={startTimer}
                className="bg-blue-600 text-white py-2 px-6 rounded-md hover:bg-blue-700 transition duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                Start
              </button>
            ) : (
              <>
                {!isPaused ? (
                  <button
                    onClick={pauseTimer}
                    className="bg-yellow-500 text-white py-2 px-6 rounded-md hover:bg-yellow-600 transition duration-300 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  >
                    Pause
                  </button>
                ) : (
                  <button
                    onClick={resumeTimer}
                    className="bg-green-500 text-white py-2 px-6 rounded-md hover:bg-green-600 transition duration-300 focus:outline-none focus:ring-2 focus:ring-green-500"
                  >
                    Resume
                  </button>
                )}
                <button
                  onClick={resetTimer}
                  className="bg-red-500 text-white py-2 px-6 rounded-md hover:bg-red-600 transition duration-300 focus:outline-none focus:ring-2 focus:ring-red-500"
                >
                  Reset
                </button>
              </>
            )}
          </div>

          {/* Hidden audio element for timer completion sound */}
          <audio ref={audioRef}>
            <source src="https://assets.mixkit.co/sfx/preview/mixkit-alarm-digital-clock-beep-989.mp3" type="audio/mpeg" />
            Your browser does not support the audio element.
          </audio>
        </div>
      </div>
    </>
  );
};

export default CountdownTimer;
